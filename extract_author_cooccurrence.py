#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取原文作者和根微博作者的共现关系，并计算权重
"""

import pandas as pd
import numpy as np
from collections import Counter

def extract_author_cooccurrence(input_file, output_file):
    """
    从CSV文件中提取原文作者和根微博作者的共现关系
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    print(f"正在读取文件: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='gb18030')
        print(f"成功读取文件，共 {len(df)} 行数据")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 检查是否包含所需字段
    required_columns = ['原文作者', '根微博作者']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"缺少必要字段: {missing_columns}")
        return
    
    print("开始提取原文作者和根微博作者的共现关系...")
    
    # 过滤掉空值的行
    valid_rows = df.dropna(subset=['原文作者', '根微博作者'])
    print(f"有效数据行数: {len(valid_rows)}")
    
    # 提取原文作者和根微博作者的组合
    author_pairs = []
    for _, row in valid_rows.iterrows():
        original_author = str(row['原文作者']).strip()
        root_author = str(row['根微博作者']).strip()
        
        # 跳过空字符串或'nan'
        if original_author and original_author != 'nan' and root_author and root_author != 'nan':
            author_pairs.append((original_author, root_author))
    
    print(f"提取到 {len(author_pairs)} 个有效的作者对")
    
    # 计算共现次数
    pair_counts = Counter(author_pairs)
    print(f"去重后共有 {len(pair_counts)} 个唯一的作者对")
    
    # 创建结果DataFrame
    result_data = []
    for (original_author, root_author), weight in pair_counts.items():
        result_data.append({
            '原文作者': original_author,
            '根微博作者': root_author,
            '权重': weight
        })
    
    result_df = pd.DataFrame(result_data)
    
    # 按权重降序排列
    result_df = result_df.sort_values('权重', ascending=False).reset_index(drop=True)
    
    # 保存结果
    try:
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_file}")
        print(f"共生成 {len(result_df)} 条记录")
        
        # 显示前10条结果
        print("\n前10条结果预览:")
        print(result_df.head(10).to_string(index=False))
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"权重最大值: {result_df['权重'].max()}")
        print(f"权重最小值: {result_df['权重'].min()}")
        print(f"权重平均值: {result_df['权重'].mean():.2f}")
        print(f"权重中位数: {result_df['权重'].median()}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    input_file = "1.csv"
    output_file = "作者共现关系.csv"
    
    extract_author_cooccurrence(input_file, output_file)
